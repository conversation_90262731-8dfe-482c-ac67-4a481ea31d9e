import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';

import '../../../core/routes/app_routes.dart';
import '../enums/chat_type.dart';

/// Modal for selecting chat type when creating a new chat
class ChatTypeSelectionModal extends StatelessWidget {
  const ChatTypeSelectionModal({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: colorScheme.onSurfaceVariant.withValues(alpha: 0.4),
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
          ),
          SizedBox(height: 20.h),

          // Title
          Text(
            'Start a new chat',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),

          // Subtitle
          Text(
            'Choose the type of conversation you want to start',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 24.h),

          // Chat type options
          _buildChatTypeOption(
            context,
            title: 'Direct Message',
            description: 'Private conversation with one person',
            icon: Symbols.person,
            onTap: () => _onChatTypeSelected(context, ChatType.oneToOne),
          ),
          SizedBox(height: 16.h),

          _buildChatTypeOption(
            context,
            title: 'Group Chat',
            description: 'Chat with multiple people',
            icon: Symbols.group,
            onTap: () => _onChatTypeSelected(context, ChatType.groupChat),
          ),
          SizedBox(height: 16.h),

          _buildChatTypeOption(
            context,
            title: 'Study Group',
            description: 'Focused group for studying together',
            icon: Symbols.groups,
            onTap: () => _onChatTypeSelected(context, ChatType.studyGroup),
          ),
          SizedBox(height: 16.h),

          _buildChatTypeOption(
            context,
            title: 'Project Team',
            description: 'Collaborate on projects and assignments',
            icon: Symbols.work_history,
            onTap: () => _onChatTypeSelected(context, ChatType.projectTeam),
          ),

          SizedBox(height: 24.h),

          // Cancel button
          SizedBox(
            width: double.infinity,
            child: TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 16.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Text(
                'Cancel',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatTypeOption(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            // Icon
            Container(
              width: 48.w,
              height: 48.h,
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Icon(
                icon,
                color: colorScheme.onPrimaryContainer,
                size: 24.sp,
              ),
            ),
            SizedBox(width: 16.w),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    description,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),

            // Arrow
            Icon(
              Symbols.chevron_right,
              color: colorScheme.onSurfaceVariant,
              size: 20.sp,
            ),
          ],
        ),
      ),
    );
  }

  void _onChatTypeSelected(BuildContext context, ChatType chatType) {
    Navigator.of(context).pop();

    // Navigate to appropriate flow based on chat type
    if (chatType == ChatType.oneToOne) {
      // For one-to-one, go directly to participant selection
      context.pushNamed(
        RouteNames.createChat,
        queryParameters: {'type': chatType.name, 'step': 'participants'},
      );
    } else {
      // For group chats, go through the full flow starting with participant selection
      context.pushNamed(
        RouteNames.createChat,
        queryParameters: {'type': chatType.name, 'step': 'participants'},
      );
    }
  }
}
