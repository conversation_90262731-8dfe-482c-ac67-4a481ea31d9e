import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/routes/app_routes.dart';
import '../../widgets/chat_type_selection_modal.dart';
import 'sections/chat_search_section.dart';
import 'sections/chat_categories_section.dart';
import 'sections/chat_list_section.dart';

/// Main chat list screen showing all conversations
class ChatListScreen extends ConsumerStatefulWidget {
  const ChatListScreen({super.key});

  @override
  ConsumerState<ChatListScreen> createState() => _ChatListScreenState();
}

class _ChatListScreenState extends ConsumerState<ChatListScreen> {
  String _selectedCategory = 'All';
  String _searchQuery = '';

  void _onCategorySelected(String category) {
    setState(() {
      _selectedCategory = category;
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
  }

  void _onCreateChat() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const ChatTypeSelectionModal(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Chat',
          style: theme.textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () => context.pushNamed(RouteNames.chatSearch),
            icon: Icon(Symbols.search, color: colorScheme.onSurface),
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Search section
            ChatSearchSection(
              searchQuery: _searchQuery,
              onSearchChanged: _onSearchChanged,
            ),

            // Categories section
            ChatCategoriesSection(
              selectedCategory: _selectedCategory,
              onCategorySelected: _onCategorySelected,
            ),

            // Chat list
            Expanded(
              child: ChatListSection(
                selectedCategory: _selectedCategory,
                searchQuery: _searchQuery,
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _onCreateChat,
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        child: const Icon(Symbols.add),
      ),
    );
  }
}
