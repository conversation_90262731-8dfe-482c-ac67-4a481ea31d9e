import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../../core/widgets/buttons/custom_button.dart';
import '../../../enums/chat_type.dart';
import '../../../widgets/participant_avatar.dart';

/// Section for selecting participants when creating a new chat
class ParticipantSelectionSection extends StatefulWidget {
  /// The selected chat type
  final ChatType chatType;

  /// Callback when participants are selected
  final Function(List<String>) onParticipantsSelected;

  /// Callback to go back to previous step
  final VoidCallback onBack;

  const ParticipantSelectionSection({
    super.key,
    required this.chatType,
    required this.onParticipantsSelected,
    required this.onBack,
  });

  @override
  State<ParticipantSelectionSection> createState() => _ParticipantSelectionSectionState();
}

class _ParticipantSelectionSectionState extends State<ParticipantSelectionSection> {
  final TextEditingController _searchController = TextEditingController();
  final Set<String> _selectedUserIds = {};
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // TODO: Replace with actual users from provider
    final availableUsers = _getAvailableUsers();
    final filteredUsers = _filterUsers(availableUsers, _searchQuery);

    final maxParticipants = _getMaxParticipants();
    final minParticipants = _getMinParticipants();

    return Padding(
      padding: EdgeInsets.all(24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and back button
          Row(
            children: [
              IconButton(
                onPressed: widget.onBack,
                icon: Icon(
                  Symbols.arrow_back,
                  color: colorScheme.onSurface,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Add Participants',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    Text(
                      _getParticipantInstructions(),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 24.h),

          // Search bar
          TextField(
            controller: _searchController,
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
            decoration: InputDecoration(
              hintText: 'Search users...',
              prefixIcon: Icon(Symbols.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                      },
                      icon: Icon(Symbols.close),
                    )
                  : null,
              filled: true,
              fillColor: colorScheme.surfaceContainerHighest,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide.none,
              ),
            ),
          ),

          SizedBox(height: 16.h),

          // Selected participants count
          Row(
            children: [
              Text(
                'Selected: ${_selectedUserIds.length}',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
              ),
              if (maxParticipants != null) ...[
                Text(
                  ' / $maxParticipants',
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ],
          ),

          SizedBox(height: 16.h),

          // Selected users preview
          if (_selectedUserIds.isNotEmpty) ...[
            SizedBox(
              height: 60.h,
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedUserIds.length,
                separatorBuilder: (context, index) => SizedBox(width: 8.w),
                itemBuilder: (context, index) {
                  final userId = _selectedUserIds.elementAt(index);
                  return _buildSelectedUserChip(userId);
                },
              ),
            ),
            SizedBox(height: 16.h),
          ],

          // Available users list
          Expanded(
            child: filteredUsers.isEmpty
                ? _buildEmptyState(context)
                : ListView.separated(
                    itemCount: filteredUsers.length,
                    separatorBuilder: (context, index) => SizedBox(height: 8.h),
                    itemBuilder: (context, index) {
                      final user = filteredUsers[index];
                      final isSelected = _selectedUserIds.contains(user['id']);
                      final canSelect = !isSelected && 
                          (maxParticipants == null || _selectedUserIds.length < maxParticipants);
                      
                      return _buildUserTile(user, isSelected, canSelect);
                    },
                  ),
          ),

          // Continue button
          SizedBox(height: 16.h),
          SizedBox(
            width: double.infinity,
            child: CustomButton.primary(
              text: 'Continue',
              onPressed: _selectedUserIds.length >= minParticipants
                  ? () => widget.onParticipantsSelected(_selectedUserIds.toList())
                  : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedUserChip(String userId) {
    final userName = _getUserName(userId);

    return Container(
      padding: EdgeInsets.all(4.w),
      child: Column(
        children: [
          Stack(
            children: [
              ParticipantAvatar(
                participantId: userId,
                participantName: userName,
                size: 32.w,
              ),
              Positioned(
                right: -4.w,
                top: -4.h,
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedUserIds.remove(userId);
                    });
                  },
                  child: Container(
                    width: 16.w,
                    height: 16.w,
                    decoration: BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Symbols.close,
                      size: 10.sp,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Text(
            userName.split(' ').first,
            style: Theme.of(context).textTheme.labelSmall,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildUserTile(Map<String, dynamic> user, bool isSelected, bool canSelect) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return InkWell(
      onTap: canSelect || isSelected ? () {
        setState(() {
          if (isSelected) {
            _selectedUserIds.remove(user['id']);
          } else {
            _selectedUserIds.add(user['id']);
          }
        });
      } : null,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: isSelected 
              ? colorScheme.primaryContainer.withValues(alpha: 0.3)
              : colorScheme.surface,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: isSelected 
                ? colorScheme.primary
                : colorScheme.outline.withValues(alpha: 0.2),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            ParticipantAvatar(
              participantId: user['id'],
              participantName: user['name'],
              size: 40.w,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user['name'],
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: canSelect || isSelected 
                          ? colorScheme.onSurface 
                          : colorScheme.onSurface.withValues(alpha: 0.5),
                    ),
                  ),
                  if (user['email'] != null) ...[
                    SizedBox(height: 2.h),
                    Text(
                      user['email'],
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: canSelect || isSelected 
                            ? colorScheme.onSurface.withValues(alpha: 0.7)
                            : colorScheme.onSurface.withValues(alpha: 0.4),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Symbols.check_circle,
                color: colorScheme.primary,
                size: 24.sp,
              )
            else if (!canSelect)
              Icon(
                Symbols.block,
                color: colorScheme.onSurface.withValues(alpha: 0.4),
                size: 24.sp,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Symbols.person_search,
            size: 64.sp,
            color: colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          SizedBox(height: 16.h),
          Text(
            _searchQuery.isNotEmpty ? 'No users found' : 'No available users',
            style: theme.textTheme.titleMedium?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            _searchQuery.isNotEmpty 
                ? 'Try adjusting your search terms.'
                : 'All users are already in your contacts.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getParticipantInstructions() {
    switch (widget.chatType) {
      case ChatType.oneToOne:
        return 'Select one person to chat with';
      case ChatType.groupChat:
        return 'Select people to add to your group';
      case ChatType.studyGroup:
        return 'Add members to your study group';
      case ChatType.projectTeam:
        return 'Add team members to collaborate';
      case ChatType.classroomDiscussion:
        return 'Add students and teachers';
      case ChatType.parentGroup:
        return 'Add parents to the group';
      case ChatType.teacherCircle:
        return 'Add teachers to the circle';
    }
  }

  int? _getMaxParticipants() {
    switch (widget.chatType) {
      case ChatType.oneToOne:
        return 1;
      case ChatType.studyGroup:
        return 10;
      case ChatType.projectTeam:
        return 8;
      default:
        return null;
    }
  }

  int _getMinParticipants() {
    switch (widget.chatType) {
      case ChatType.oneToOne:
        return 1;
      default:
        return 1;
    }
  }

  List<Map<String, dynamic>> _filterUsers(List<Map<String, dynamic>> users, String query) {
    if (query.isEmpty) return users;
    
    return users.where((user) {
      final name = user['name'].toString().toLowerCase();
      final email = user['email']?.toString().toLowerCase() ?? '';
      final searchQuery = query.toLowerCase();
      
      return name.contains(searchQuery) || email.contains(searchQuery);
    }).toList();
  }

  String _getUserName(String userId) {
    // TODO: Get actual user name from user data
    return 'User $userId';
  }

  // TODO: Replace with actual data from provider
  List<Map<String, dynamic>> _getAvailableUsers() {
    return [
      // Mock users will be added when we implement mock data integration
    ];
  }
}
