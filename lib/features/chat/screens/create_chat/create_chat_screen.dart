import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';

import '../../enums/chat_type.dart';
import 'sections/chat_type_selection_section.dart';
import 'sections/participant_selection_section.dart';
import 'sections/chat_details_section.dart';
import 'sections/create_chat_actions_section.dart';

/// Screen for creating a new chat
class CreateChatScreen extends ConsumerStatefulWidget {
  const CreateChatScreen({super.key, this.initialChatType, this.initialStep});

  final ChatType? initialChatType;
  final String? initialStep;

  @override
  ConsumerState<CreateChatScreen> createState() => _CreateChatScreenState();
}

class _CreateChatScreenState extends ConsumerState<CreateChatScreen> {
  final PageController _pageController = PageController();
  int _currentStep = 0;

  ChatType? _selectedChatType;
  List<String> _selectedParticipants = [];
  String _chatTitle = '';
  String _chatDescription = '';

  @override
  void initState() {
    super.initState();
    _initializeFromParameters();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _initializeFromParameters() {
    if (widget.initialChatType != null) {
      _selectedChatType = widget.initialChatType;
    }

    if (widget.initialStep == 'participants' && _selectedChatType != null) {
      // Start from participant selection step
      _currentStep = 1;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _pageController.animateToPage(
          1,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      });
    }
  }

  void _onChatTypeSelected(ChatType chatType) {
    setState(() {
      _selectedChatType = chatType;
    });
    _nextStep();
  }

  void _onParticipantsSelected(List<String> participants) {
    setState(() {
      _selectedParticipants = participants;
    });
    _nextStep();
  }

  void _onChatDetailsCompleted(String title, String description) {
    setState(() {
      _chatTitle = title;
      _chatDescription = description;
    });
    _nextStep();
  }

  void _nextStep() {
    if (_currentStep < 2) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _onCreateChat() {
    // TODO: Implement chat creation logic
    context.pop();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Create Chat',
          style: theme.textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(Symbols.arrow_back, color: colorScheme.onSurface),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Progress indicator
            LinearProgressIndicator(
              value: (_currentStep + 1) / 3,
              backgroundColor: colorScheme.surfaceContainerHighest,
              valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
            ),

            // Page view
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  // Step 1: Chat type selection
                  ChatTypeSelectionSection(
                    onChatTypeSelected: _onChatTypeSelected,
                  ),

                  // Step 2: Participant selection
                  ParticipantSelectionSection(
                    chatType: _selectedChatType!,
                    onParticipantsSelected: _onParticipantsSelected,
                    onBack: _previousStep,
                  ),

                  // Step 3: Chat details
                  ChatDetailsSection(
                    chatType: _selectedChatType!,
                    participants: _selectedParticipants,
                    onDetailsCompleted: _onChatDetailsCompleted,
                    onBack: _previousStep,
                  ),
                ],
              ),
            ),

            // Actions
            if (_currentStep == 2)
              CreateChatActionsSection(
                onCreateChat: _onCreateChat,
                onBack: _previousStep,
              ),
          ],
        ),
      ),
    );
  }
}
